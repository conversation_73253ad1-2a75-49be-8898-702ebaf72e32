
class GetDropDownDataListResponse {
  int? statusCode;
  Headers? headers;
  Body? body;

  GetDropDownDataListResponse({this.statusCode, this.headers, this.body});

  GetDropDownDataListResponse.fromJson(Map<String, dynamic> json) {
    statusCode = json['statusCode'];
    headers =
    json['headers'] != null ? Headers.fromJson(json['headers']) : null;
    body = json['body'] != null ? Body.fromJson(json['body']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['statusCode'] = statusCode;
    if (headers != null) {
      data['headers'] = headers!.toJson();
    }
    if (body != null) {
      data['body'] = body!.toJson();
    }
    return data;
  }
}

class Headers {
  String? contentType;

  Headers({this.contentType});

  Headers.fromJson(Map<String, dynamic> json) {
    contentType = json['Content-Type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Content-Type'] = contentType;
    return data;
  }
}

class Body {
  List<Intention>? intention;
  List<InterestIn>? interestIn;
  List<Hobbies>? hobbies;
  List<RelationshipType>? relationshipType;
  List<Genders>? genders;
  List<Genders>? languages;

  Body(
      {this.intention,
        this.interestIn,this.languages,
        this.hobbies,
        this.relationshipType,
        this.genders});

  Body.fromJson(Map<String, dynamic> json) {
    if (json['intention'] != null) {
      intention = <Intention>[];
      json['intention'].forEach((v) {
        intention!.add(Intention.fromJson(v));
      });
    }
    if (json['interest_in'] != null) {
      interestIn = <InterestIn>[];
      json['interest_in'].forEach((v) {
        interestIn!.add(InterestIn.fromJson(v));
      });
    }
    if (json['hobbies'] != null) {
      hobbies = <Hobbies>[];
      json['hobbies'].forEach((v) {
        hobbies!.add(Hobbies.fromJson(v));
      });
    }
    if (json['relationship_type'] != null) {
      relationshipType = <RelationshipType>[];
      json['relationship_type'].forEach((v) {
        relationshipType!.add(RelationshipType.fromJson(v));
      });
    }
    if (json['genders'] != null) {
      genders = <Genders>[];
      json['genders'].forEach((v) {
        genders!.add(Genders.fromJson(v));
      });
    }
    if (json['spoken_languages'] != null) {
      languages = <Genders>[];
      json['spoken_languages'].forEach((v) {
        languages!.add(Genders.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (intention != null) {
      data['intention'] = intention!.map((v) => v.toJson()).toList();
    }
    if (interestIn != null) {
      data['interest_in'] = interestIn!.map((v) => v.toJson()).toList();
    }
    if (hobbies != null) {
      data['hobbies'] = hobbies!.map((v) => v.toJson()).toList();
    }
    if (relationshipType != null) {
      data['relationship_type'] =
          relationshipType!.map((v) => v.toJson()).toList();
    }
    if (genders != null) {
      data['genders'] = genders!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Intention {
  String? id;
  String? name;
  bool intentionCheck=false;

  Intention({this.id, this.name,this.intentionCheck=false});

  Intention.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}


class InterestIn {
  String? id;
  String? name;


  InterestIn({this.id, this.name});

  InterestIn.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class Hobbies {
  String? name;
  String? category;
  bool hobbiesCheck=false;

  Hobbies({this.name, this.category,this.hobbiesCheck=false});

  Hobbies.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    category = json['category'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['category'] = category;
    return data;
  }
}

class RelationshipType {
  String? name;
  bool checkRelation=false;

  RelationshipType({this.name,this.checkRelation=false});

  RelationshipType.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    return data;
  }}


  class Genders {
  String? name;

  Genders({this.name});

  Genders.fromJson(Map<String, dynamic> json) {
  name = json['name'];
  }

  Map<String, dynamic> toJson() {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['name'] = name;
  return data;
  }


}
/*


class SpokenLanguages{

  String? langName;
  bool spokenCheck=false;

  SpokenLanguages(this.langName, this.spokenCheck);


 static List<SpokenLanguages>getLanguages(){

    List<SpokenLanguages>languagesList =<SpokenLanguages>[];
    languagesList.add(SpokenLanguages("Hindi",false));
    languagesList.add(SpokenLanguages("English",false));
    languagesList.add(SpokenLanguages("Italian",false));
    languagesList.add(SpokenLanguages("French",false));
    languagesList.add(SpokenLanguages("Spanish",false));
    return languagesList;

  }

  SpokenLanguages spokenLanguages  = SpokenLanguages("Hindi", false);

}*/
