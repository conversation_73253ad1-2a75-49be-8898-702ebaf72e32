
class GetSignUpResponse {
  int? statusCode;
  Headers? headers;
  Body? body;

  GetSignUpResponse({this.statusCode, this.headers, this.body});

  GetSignUpResponse.fromJson(Map<String, dynamic> json) {
    statusCode = json['statusCode'];
    headers =
    json['headers'] != null ? new Headers.fromJson(json['headers']) : null;
    body = json['body'] != null ? new Body.fromJson(json['body']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['statusCode'] = this.statusCode;
    if (this.headers != null) {
      data['headers'] = this.headers!.toJson();
    }
    if (this.body != null) {
      data['body'] = this.body!.toJson();
    }
    return data;
  }
}

class Headers {
  String? contentType;

  Headers({this.contentType});

  Headers.fromJson(Map<String, dynamic> json) {
    contentType = json['Content-Type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['Content-Type'] = this.contentType;
    return data;
  }
}

class Body {
  String? accessToken;
  int? userId;

  Body({this.accessToken, this.userId});

  Body.fromJson(Map<String, dynamic> json) {
    accessToken = json['access_token'];
    userId = json['user_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['access_token'] = this.accessToken;
    data['user_id'] = this.userId;
    return data;
  }
}
