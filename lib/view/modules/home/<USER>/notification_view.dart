import 'dart:convert';
import 'dart:developer';

import 'package:banana/constants/my_app_button_with_icons.dart';
import 'package:banana/constants/my_colors.dart';
import 'package:banana/constants/my_fonts.dart';
import 'package:banana/constants/my_images.dart';
import 'package:banana/constants/my_string.dart';
import 'package:banana/constants/sized_box.dart';
import 'package:banana/core/widgets/shimmer_effect.dart';
import 'package:banana/response/GetHomeDataResponse.dart';
import 'package:banana/response/GetMyNotificationResponse.dart';
import 'package:banana/view/modules/home/<USER>/notifications_model.dart';
import 'package:banana/view/modules/home/<USER>/events_detail_view.dart';
import 'package:banana/view/modules/people/controller/people_controller.dart'
    show PeopleController;
import 'package:banana/view/modules/people/model/people_model.dart' show People;
import 'package:banana/view/modules/profile/view/private_tryst_request_view.dart';
import 'package:banana/view/modules/requests/controller/request_controller.dart'
    show RequestController;
import 'package:banana/view/navigation/controller/navigation_controller.dart';
import 'package:flutter/cupertino.dart' show CupertinoIcons;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:readmore/readmore.dart';

import '../../../../core/constants/assets_constants.dart';
import '../../../../core/constants/color_constants.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/text_widgets.dart';
import '../../../../core/widgets/widgets.dart';
import '../../inbox/controller/chat_controller.dart';
import '../../inbox/model/chat_model.dart';
import '../../inbox/view/chat_view.dart';
import '../../people/view/people_detail_view.dart';
import '../controller/home_controller.dart';
import 'dialogs/add_note_dialog.dart';

class NotificationView extends StatefulWidget {
  final List<NotificationsList>? notificationsList;
  const NotificationView({super.key, this.notificationsList});

  @override
  State<NotificationView> createState() => _NotificationViewState();
}

class _NotificationViewState extends State<NotificationView> {
  String typeButton = MyString.accepted;

  late HomeController homeController;

  @override
  void initState() {
    super.initState();
    homeController = Get.find();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          centerTitle: true,
          automaticallyImplyLeading: true,
          elevation: 0,
          backgroundColor: Colors.white,
          title: Texts.textBold("Notifications", size: 18, color: Colors.black),
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            await homeController.fetchNotifications();
          },
          color: ColorConstants.primaryColor,
          backgroundColor: ColorConstants.secondaryColor,
          child: Obx(() => homeController.isNotificationLoading.value
              ? Padding(
                  padding: const EdgeInsets.all(15.0),
                  child: ShimmerListSkeleton(),
                )
              : homeController.notifications.isEmpty
                  ? _buildEmptyListView()
                  : ListView.separated(
                      padding:
                          const EdgeInsets.only(left: 10, right: 10, top: 0),
                      itemCount: homeController.notifications.length,
                      physics: const AlwaysScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        Notifications notifications =
                            homeController.notifications[index];
                        return Dismissible(
                            key: UniqueKey(),
                            onDismissed: (direction) {
                              homeController.removeNotifications(
                                  notifications.notificationId.toString(),
                                  index);
                            },
                            child: notifications.notificationType ==
                                    "ATTENDANCE_REQUEST"
                                ? notifications.message!.contains("Event") ?buildAttendanceEventRequestNotificationCard(
                                    context, notifications, index):buildAttendanceRequestNotificationCard(
                            context, notifications, index)
                                : notifications.notificationType ==
                                        "REDIRECT_CHAT"
                                    ? buildPrivateChatNotificationCard(
                                        context, notifications)
                                    : notifications.notificationType ==
                                            "POST_THOUGHT"
                                        ? buildPostThoughtNotificationCard(
                                            context, notifications)
                                        : notifications.notificationType ==
                                                "RATING"
                                            ? buildRatingNotificationCard(
                                                context, notifications, index)
                                            : notifications.notificationType ==
                                                    "ASKOUT_NEW_TIME"
                                                ? buildAskoutNewTimeNotificationCard(
                                                    context,
                                                    notifications,
                                                    index)
                                                : buildNotificationCard(
                                                    context,
                                                    false,
                                                    true,
                                                    notifications));
                      },
                      separatorBuilder: (BuildContext context, int index) {
                        return Divider(height: 1, color: Colors.black12);
                      },
                    )),
        ));
  }

  // Empty list view that still allows pull-to-refresh
  Widget _buildEmptyListView() {
    return ListView(
      physics: AlwaysScrollableScrollPhysics(),
      children: [
        SizedBox(height: MediaQuery.of(context).size.height * 0.2),
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.notifications_off_outlined,
                size: 70,
                color: Colors.grey[400],
              ),
              SizedBox(height: 16),
              Texts.textMedium(
                "No notifications yet",
                size: 16,
                color: Colors.grey[600],
              ),
              SizedBox(height: 8),
              Texts.textMedium(
                "Pull down to refresh",
                size: 14,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Container buildAttendanceRequestNotificationCard(
      BuildContext context, Notifications notifications, int index) {
    return Container(
        alignment: Alignment.center,
        margin: EdgeInsets.only(left: 0, right: 0, top: 0),
        padding: EdgeInsets.only(left: 15, right: 8, top: 10, bottom: 17),
        decoration: BoxDecoration(
          color: MyColors.colorWhite,
          //borderRadius: BorderRadius.all(Radius.circular(15.0))
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                    onTap: () {},
                    child: AdvancedAvatar(
                      animated: true,
                      decoration: BoxDecoration(
                        color: ColorConstants.textColor,
                        shape: BoxShape.circle,
                      ),
                      size: 35,
                      foregroundDecoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: ColorConstants.primaryColor,
                          width: 0.0,
                        ),
                      ),
                      child: Widgets.networkImage(
                        notifications.notificationThumbnail ?? "",
                        width: 100,
                        height: 100,
                      ),
                    )),
                wSized15,
                Flexible(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Texts.textBlock(
                          color: ColorConstants.secondaryColor,
                          notifications.title ?? "",
                          size: 13,
                          fontWeight: FontWeight.w600,
                          maxline: 2),
                      SizedBox(
                        height: 2,
                      ),
                      Texts.textMedium(notifications.message ?? "",
                          size: 11, color: ColorConstants.textColor),
                      SizedBox(
                        height: 4,
                      ),
                      Row(
                        children: [
                          Icon(
                            Icons.location_pin,
                            size: 11,
                            color: Colors.red,
                          ),
                          Texts.textMedium(
                              jsonDecode(notifications.requestId ?? "")[
                                  'location_name'],
                              size: 10,
                              color: ColorConstants.textColor),
                        ],
                      ),
                      SizedBox(
                        height: 2,
                      ),
                      Row(
                        children: [
                          Icon(
                            Icons.calendar_month,
                            size: 11,
                          ),
                          Texts.textMedium(
                              " ${jsonDecode(notifications.requestId ?? "")['proposed_date']} ${jsonDecode(notifications.requestId ?? "")['proposed_hour']}",
                              size: 10,
                              color: ColorConstants.textColor),
                        ],
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      notifications.title == "Incoming Askout Request"
                          ? Row(children: [
                              InkWell(
                                onTap: () {
                                  Get.find<PeopleController>()
                                          .peopleCard
                                          .value =
                                      People(userId: notifications.userId);
                                  Get.find<PeopleController>()
                                      .fetchProfileDetails();
                                  Get.to(() => PeopleDetailView());
                                },
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 5),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                          color: Colors.black, width: .5)),
                                  child: Text(
                                    "View Profile",
                                    style: TextStyle(
                                        color: Colors.black54,
                                        fontSize: 8,
                                        fontWeight: FontWeight.w600),
                                  ),
                                ),
                              ),
                              SizedBox(
                                width: 5,
                              ),
                              InkWell(
                                  onTap: () async {
                                    var result =
                                        await askOutDeclineRequest(context);
                                    if (result != null) {
                                      homeController.cancelRequest(
                                          context,
                                          result,
                                          jsonDecode(notifications.requestId)[
                                                  'request_id']
                                              .toString(),
                                          index,
                                          notifications);
                                    }
                                  },
                                  child: CircleAvatar(
                                    radius: 12,
                                    child: Icon(
                                      Icons.clear,
                                      color: Colors.white,
                                      size: 12,
                                    ),
                                    backgroundColor: ColorConstants.redColor,
                                  )),
                              SizedBox(
                                width: 5,
                              ),
                              InkWell(
                                onTap: () {
                                  homeController.acceptRequest(
                                      context,
                                      jsonDecode(notifications.requestId)[
                                              'request_id']
                                          .toString(),
                                      index,
                                      notifications);
                                },
                                child: CircleAvatar(
                                  radius: 12,
                                  child: Icon(
                                    Icons.check,
                                    color: Colors.white,
                                    size: 12,
                                  ),
                                  backgroundColor: ColorConstants.greenColor,
                                ),
                              ),
                            ])
                          : Row(children: [
                              InkWell(
                                onTap: () {
                                  Get.find<PeopleController>()
                                          .peopleCard
                                          .value =
                                      People(userId: notifications.userId);
                                  Get.find<PeopleController>()
                                      .fetchProfileDetails();
                                  Get.to(() => PeopleDetailView());
                                },
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 5),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                          color: Colors.black, width: .5)),
                                  child: Text(
                                    "View Profile",
                                    style: TextStyle(
                                        color: Colors.black54,
                                        fontSize: 8,
                                        fontWeight: FontWeight.w600),
                                  ),
                                ),
                              ),
                              SizedBox(
                                width: 5,
                              ),
                              InkWell(
                                  onTap: () async {
                                    var result =
                                        await askOutDeclineRequest(context);
                                    if (result != null) {
                                      homeController.cancelRequest(
                                          context,
                                          result,
                                          jsonDecode(notifications.requestId)[
                                                  'request_id']
                                              .toString(),
                                          index,
                                          notifications);
                                    }
                                  },
                                  child: CircleAvatar(
                                    radius: 12,
                                    child: Icon(
                                      Icons.clear,
                                      color: Colors.white,
                                      size: 12,
                                    ),
                                    backgroundColor: ColorConstants.redColor,
                                  )),
                              SizedBox(
                                width: 5,
                              ),
                              InkWell(
                                onTap: () {
                                  homeController.acceptRequest(
                                      context,
                                      jsonDecode(notifications.requestId)[
                                              'request_id']
                                          .toString(),
                                      index,
                                      notifications);
                                },
                                child: CircleAvatar(
                                  radius: 12,
                                  child: Icon(
                                    Icons.check,
                                    color: Colors.white,
                                    size: 12,
                                  ),
                                  backgroundColor: ColorConstants.greenColor,
                                ),
                              ),
                            ]),
                      SizedBox(
                        height: 8,
                      ),
                      Texts.textMedium(notifications.datetime ?? "",
                          size: 10, color: ColorConstants.textColor),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ));
  }

  Container buildAttendanceEventRequestNotificationCard(
      BuildContext context, Notifications notifications, int index) {
    return Container(
        alignment: Alignment.center,
        margin: EdgeInsets.only(left: 0, right: 0, top: 0),
        padding: EdgeInsets.only(left: 15, right: 8, top: 10, bottom: 17),
        decoration: BoxDecoration(
          color: MyColors.colorWhite,
          //borderRadius: BorderRadius.all(Radius.circular(15.0))
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                    onTap: () {},
                    child: AdvancedAvatar(
                      animated: true,
                      decoration: BoxDecoration(
                        color: ColorConstants.textColor,
                        shape: BoxShape.circle,
                      ),
                      size: 35,
                      foregroundDecoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: ColorConstants.primaryColor,
                          width: 0.0,
                        ),
                      ),
                      child: Widgets.networkImage(
                        notifications.notificationThumbnail ?? "",
                        width: 100,
                        height: 100,
                      ),
                    )),
                wSized15,
                Flexible(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Texts.textBlock(
                          color: ColorConstants.secondaryColor,
                          notifications.title ?? "",
                          size: 13,
                          fontWeight: FontWeight.w600,
                          maxline: 2),
                      SizedBox(
                        height: 2,
                      ),
                      Texts.textMedium(notifications.message ?? "",
                          size: 11, color: ColorConstants.textColor),
                      SizedBox(
                        height: 4,
                      ),
                      Row(children: [
                        InkWell(
                          onTap: () {
                            Get.find<HomeController>().selectedEvent.value =
                                EventsCard(
                                    eventId:
                                        notifications.requestId.toString());
                            Get.find<HomeController>()
                                .fetchEventTrystDetailsApi();
                            Get.to(() => EventsDetailView(
                                  eventId: notifications.requestId.toString(),
                                  openFrom: "home",
                                ));
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 5),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                border:
                                    Border.all(color: Colors.black, width: .5)),
                            child: Text(
                              "View Details",
                              style: TextStyle(
                                  color: Colors.black54,
                                  fontSize: 8,
                                  fontWeight: FontWeight.w600),
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 5,
                        ),
                        InkWell(
                            onTap: () async {
                              var result = await askOutDeclineRequest(context);
                              if (result != null) {
                                homeController.cancelRequest(
                                    context,
                                    result,
                                    notifications.requestId
                                        .toString(),
                                    index,
                                    notifications);
                              }
                            },
                            child: CircleAvatar(
                              radius: 12,
                              child: Icon(
                                Icons.clear,
                                color: Colors.white,
                                size: 12,
                              ),
                              backgroundColor: ColorConstants.redColor,
                            )),
                        SizedBox(
                          width: 5,
                        ),
                        InkWell(
                          onTap: () {
                            homeController.acceptRequest(
                                context,
                                notifications.requestId
                                    .toString(),
                                index,
                                notifications);
                          },
                          child: CircleAvatar(
                            radius: 12,
                            child: Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 12,
                            ),
                            backgroundColor: ColorConstants.greenColor,
                          ),
                        ),
                      ]),
                      SizedBox(
                        height: 8,
                      ),
                      Texts.textMedium(notifications.datetime ?? "",
                          size: 10, color: ColorConstants.textColor),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ));
  }

  Container buildRatingNotificationCard(
      BuildContext context, Notifications notifications, int index) {
    return Container(
        alignment: Alignment.center,
        margin: EdgeInsets.only(left: 0, right: 0, top: 0),
        padding: EdgeInsets.only(left: 15, right: 8, top: 10, bottom: 17),
        decoration: BoxDecoration(
          color: MyColors.colorWhite,
          //borderRadius: BorderRadius.all(Radius.circular(15.0))
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                    onTap: () {},
                    child: AdvancedAvatar(
                      animated: true,
                      decoration: BoxDecoration(
                        color: ColorConstants.textColor,
                        shape: BoxShape.circle,
                      ),
                      size: 35,
                      foregroundDecoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: ColorConstants.primaryColor,
                          width: 0.0,
                        ),
                      ),
                      child: Widgets.networkImage(
                        notifications.notificationThumbnail ?? "",
                        width: 100,
                        height: 100,
                      ),
                    )),
                wSized15,
                Flexible(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Texts.textBlock(
                          color: ColorConstants.secondaryColor,
                          notifications.title ?? "",
                          size: 13,
                          fontWeight: FontWeight.w600,
                          maxline: 2),
                      notifications.message != ""
                          ? Column(
                              children: [
                                SizedBox(
                                  height: 2,
                                ),
                                Texts.textMedium(notifications.message ?? "",
                                    size: 11, color: ColorConstants.textColor),
                              ],
                            )
                          : SizedBox(),
                      SizedBox(
                        height: 5,
                      ),
                      InkWell(
                        onTap: () {
                          showReviewBottomSheet(context, notifications, index);
                        },
                        child: Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border:
                                  Border.all(color: Colors.black, width: .5)),
                          child: Text(
                            "Write a review",
                            style: TextStyle(
                                color: Colors.black54,
                                fontSize: 8,
                                fontWeight: FontWeight.w600),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 8,
                      ),
                      Texts.textMedium(notifications.datetime ?? "",
                          size: 10, color: ColorConstants.textColor),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ));
  }

  Container buildPostThoughtNotificationCard(
      BuildContext context, Notifications notifications) {
    return Container(
        alignment: Alignment.center,
        margin: EdgeInsets.only(left: 0, right: 0, top: 0),
        padding: EdgeInsets.only(left: 15, right: 8, top: 10, bottom: 17),
        decoration: BoxDecoration(
          color: MyColors.colorWhite,
          //borderRadius: BorderRadius.all(Radius.circular(15.0))
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                    onTap: () {},
                    child: AdvancedAvatar(
                      animated: true,
                      decoration: BoxDecoration(
                        color: ColorConstants.textColor,
                        shape: BoxShape.circle,
                      ),
                      size: 35,
                      foregroundDecoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: ColorConstants.primaryColor,
                          width: 0.0,
                        ),
                      ),
                      child: Widgets.networkImage(
                        notifications.notificationThumbnail ?? "",
                        width: 100,
                        height: 100,
                      ),
                    )),
                wSized15,
                Flexible(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Texts.textBlock(
                          color: ColorConstants.secondaryColor,
                          notifications.title ?? "",
                          size: 13,
                          fontWeight: FontWeight.w600,
                          maxline: 2),
                      SizedBox(
                        height: 2,
                      ),
                      Texts.textMedium(notifications.message ?? "",
                          size: 11, color: ColorConstants.textColor),
                      SizedBox(
                        height: 5,
                      ),
                      InkWell(
                        onTap: () {
                          homeController.note.value =
                              notifications.message ?? "";
                          NoteDialog.showNoteDialog(context,
                              isNotify: true, noti: notifications);
                        },
                        child: Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border:
                                  Border.all(color: Colors.black, width: .5)),
                          child: Text(
                            "Post a thought",
                            style: TextStyle(
                                color: Colors.black54,
                                fontSize: 8,
                                fontWeight: FontWeight.w600),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 8,
                      ),
                      Texts.textMedium(notifications.datetime ?? "",
                          size: 10, color: ColorConstants.textColor),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ));
  }

  Container buildPrivateChatNotificationCard(
      BuildContext context, Notifications notifications) {
    return Container(
        alignment: Alignment.center,
        margin: EdgeInsets.only(left: 0, right: 0, top: 0),
        padding: EdgeInsets.only(left: 15, right: 8, top: 10, bottom: 17),
        decoration: BoxDecoration(
          color: MyColors.colorWhite,
          //borderRadius: BorderRadius.all(Radius.circular(15.0))
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                    onTap: () {},
                    child: AdvancedAvatar(
                      animated: true,
                      decoration: BoxDecoration(
                        color: ColorConstants.textColor,
                        shape: BoxShape.circle,
                      ),
                      size: 35,
                      foregroundDecoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: ColorConstants.primaryColor,
                          width: 0.0,
                        ),
                      ),
                      child: Widgets.networkImage(
                        notifications.notificationThumbnail ?? "",
                        width: 100,
                        height: 100,
                      ),
                    )),
                wSized15,
                Flexible(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Texts.textBlock(
                          color: ColorConstants.secondaryColor,
                          notifications.title ?? "",
                          size: 13,
                          fontWeight: FontWeight.w600,
                          maxline: 2),
                      SizedBox(
                        height: 2,
                      ),
                      Texts.textMedium(notifications.message ?? "",
                          size: 11, color: ColorConstants.textColor),
                      SizedBox(
                        height: 5,
                      ),
                      InkWell(
                        onTap: () {
                          redirectToChat(notifications);
                        },
                        child: Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border:
                                  Border.all(color: Colors.black, width: .5)),
                          child: Text(
                            "Chat now",
                            style: TextStyle(
                                color: Colors.black54,
                                fontSize: 8,
                                fontWeight: FontWeight.w600),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 8,
                      ),
                      Texts.textMedium(notifications.datetime ?? "",
                          size: 10, color: ColorConstants.textColor),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ));
  }

  Container buildAskoutNewTimeNotificationCard(
      BuildContext context, Notifications notifications, index) {
    return Container(
        alignment: Alignment.center,
        margin: EdgeInsets.only(left: 0, right: 0, top: 0),
        padding: EdgeInsets.only(left: 15, right: 8, top: 10, bottom: 17),
        decoration: BoxDecoration(
          color: MyColors.colorWhite,
          //borderRadius: BorderRadius.all(Radius.circular(15.0))
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                    onTap: () {},
                    child: AdvancedAvatar(
                      animated: true,
                      decoration: BoxDecoration(
                        color: ColorConstants.textColor,
                        shape: BoxShape.circle,
                      ),
                      size: 35,
                      foregroundDecoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: ColorConstants.primaryColor,
                          width: 0.0,
                        ),
                      ),
                      child: Widgets.networkImage(
                        notifications.notificationThumbnail ?? "",
                        width: 100,
                        height: 100,
                      ),
                    )),
                wSized15,
                Flexible(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Texts.textBlock(
                          color: ColorConstants.secondaryColor,
                          notifications.title ?? "",
                          size: 13,
                          fontWeight: FontWeight.w600,
                          maxline: 2),
                      SizedBox(
                        height: 2,
                      ),
                      Texts.textMedium(notifications.message ?? "",
                          size: 11, color: ColorConstants.textColor),
                      SizedBox(
                        height: 8,
                      ),
                      InkWell(
                        onTap: () {
                          homeController.askOutRequest(
                              context,
                              jsonDecode(notifications.requestId.toString()),
                              notifications.notificationId ?? "",
                              notifications.userId.toString(),
                              index);
                        },
                        child: Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border:
                                  Border.all(color: Colors.black, width: .5)),
                          child: Text(
                            "Accept Proposed Date",
                            style: TextStyle(
                                color: Colors.black54,
                                fontSize: 8,
                                fontWeight: FontWeight.w600),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ));
  }

  Container buildNotificationCard(BuildContext context, bool? isRequest,
      bool? isNormal, Notifications notifications) {
    return Container(
        alignment: Alignment.center,
        margin: EdgeInsets.only(left: 0, right: 0, top: 0),
        padding: EdgeInsets.only(left: 15, right: 8, top: 10, bottom: 17),
        decoration: BoxDecoration(
          color: MyColors.colorWhite,
          //borderRadius: BorderRadius.all(Radius.circular(15.0))
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                  onTap: () {},
                  child: AdvancedAvatar(
                    animated: true,
                    decoration: BoxDecoration(
                      color: ColorConstants.purpleColor,
                      shape: BoxShape.circle,
                    ),
                    size: 35,
                    child: Icon(Icons.calendar_month_outlined,
                        size: 15, color: Colors.white),
                  ),
                ),
                wSized15,
                Flexible(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Texts.textBlock(
                          color: ColorConstants.secondaryColor,
                          notifications.title ?? "",
                          size: 13,
                          fontWeight: FontWeight.w600,
                          maxline: 2),
                      notifications.message != ""
                          ? Column(
                              children: [
                                SizedBox(
                                  height: 2,
                                ),
                                Texts.textMedium(notifications.message ?? "",
                                    size: 11, color: ColorConstants.textColor),
                              ],
                            )
                          : SizedBox(),
                      SizedBox(
                        height: 5,
                      ),
                      Texts.textMedium(notifications.datetime ?? "",
                          size: 10, color: Colors.black45),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ));
  }

  askOutDeclineRequest(BuildContext context) async {
    getAskOutReasons(); // Initialize the decline request list
    int? selectedReason;

    return showDialog<String?>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          insetPadding: const EdgeInsets.symmetric(horizontal: 15),
          backgroundColor: Colors.white,
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: StatefulBuilder(
                builder: (BuildContext context, StateSetter setState) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Widgets.heightSpaceH2,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Flexible(
                            child: Texts.textBold(
                              "Let them know why you're declining.",
                              size: 20,
                              maxline: 2,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(width: 10),
                          InkWell(
                              onTap: () => Navigator.pop(context),
                              child: Icon(
                                CupertinoIcons.clear_circled_solid,
                                color: Colors.black,
                              ))
                        ],
                      ),
                      Widgets.heightSpaceH1,
                      SizedBox(
                        height: .34.sh,
                        child: ListView.builder(
                          itemCount: askOutDeclineReasons.length,
                          itemBuilder: (context, index) {
                            return Container(
                              margin: EdgeInsets.only(top: 10),
                              padding: EdgeInsets.only(left: 15, right: 15),
                              height: 40,
                              decoration: BoxDecoration(
                                  color: askOutDeclineReasons[index].reasonSe ==
                                          true
                                      ? ColorConstants.lightPrimaryColor
                                      : ColorConstants.lightGrey,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(12.0)),
                                  border: Border.all(
                                      color: askOutDeclineReasons[index]
                                                  .reasonSe ==
                                              true
                                          ? ColorConstants.blackColor
                                          : Colors.white,
                                      width: 1)),
                              child: InkWell(
                                onTap: () {
                                  setState(() {
                                    // Reset all selections
                                    for (var request in askOutDeclineReasons) {
                                      request.reasonSe = false;
                                    }
                                    // Set selected item
                                    askOutDeclineReasons[index].reasonSe = true;
                                    selectedReason = index + 1;
                                  });

                                  debugPrint(
                                      "selectLookingForList>>>  authenticationControllerselectLookingForList");
                                  setState(() {});
                                },
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Texts.textNormal(
                                      askOutDeclineReasons[index]
                                          .reasonName
                                          .toString(),
                                      color: Colors.black,
                                      size: 12,
                                    ),
                                    SvgPicture.asset(
                                        askOutDeclineReasons[index].reasonSe ==
                                                true
                                            ? Assets.selectedItem
                                            : Assets.unselectedItem,
                                        width: 16,
                                        height: 16)
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      CustomButton(
                          label: "Submit",
                          onTap: () {
                            Navigator.pop(context, selectedReason.toString());
                          }),
                      Widgets.heightSpaceH1,
                    ],
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  List<DeclineRequest> askOutDeclineReasons = <DeclineRequest>[];

  getAskOutReasons() {
    askOutDeclineReasons.clear();
    askOutDeclineReasons
        .add(DeclineRequest("Timing doesn’t work for me.", false));
    askOutDeclineReasons
        .add(DeclineRequest("Not interested, but thanks!", false));
  }

  List<DeclineRequest> eventDeclineReasons = <DeclineRequest>[];
  getEventReasons() {
    eventDeclineReasons.clear();
    eventDeclineReasons.add(DeclineRequest("Sorry, the event is full", false));
    eventDeclineReasons.add(
        DeclineRequest("Plans changed, won’t be hosting this event.", false));
    eventDeclineReasons
        .add(DeclineRequest("This event is invite-only.", false));
  }

  void showReviewBottomSheet(
      BuildContext context, Notifications notifications, int index) {
    int selectedRating = 0;
    final reviewController = TextEditingController();

    showModalBottomSheet(
      backgroundColor: Colors.white,
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (contextt, setState) {
            return Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
                left: 20,
                right: 20,
                top: 20,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Texts.textBold("Write a Review", size: 18),
                      IconButton(
                        icon: Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  SizedBox(height: 15),
                  Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(5, (index) {
                        return IconButton(
                          icon: Icon(
                            index < selectedRating
                                ? Icons.star
                                : Icons.star_border,
                            color: index < selectedRating
                                ? Colors.amber
                                : Colors.grey,
                            size: 36,
                          ),
                          onPressed: () {
                            setState(() {
                              selectedRating = index + 1;
                            });
                          },
                        );
                      }),
                    ),
                  ),
                  SizedBox(height: 15),
                  TextField(
                    controller: reviewController,
                    maxLines: 4,
                    decoration: InputDecoration(
                      hintText: "Share your experience...",
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      contentPadding: EdgeInsets.all(15),
                    ),
                  ),
                  SizedBox(height: 20),
                  CustomButton(
                    label: "Submit Review",
                    onTap: () {
                      if (selectedRating == 0) {
                        Widgets.showSnackBar("Alert", "Please select a rating");
                        return;
                      }

                      var request = {};
                      request['access_token'] =
                          homeController.userController.accessToken.value;
                      request['feedback'] = reviewController.text;
                      request['rating'] = selectedRating.toString();
                      request['from_notification'] = true;
                      request['notif_id'] = notifications.notificationId;
                      homeController.submitFeedback(request, index, context);
                    },
                  ),
                  SizedBox(height: 20),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void redirectToChat(Notifications notification) async {
    try {
      // Show loading indicator
      Get.back();
      Get.find<NavigationController>().changeIndex(3);
      // Get the chat controller
      ChatController chatController;
      try {
        chatController = Get.find<ChatController>();
      } catch (e) {
        // If controller doesn't exist, create it
        chatController = Get.put(ChatController());
      }

      // Fetch chats if needed
      if (chatController.chats.isEmpty) {
        await Get.find<ChatController>().fetchChatsBackground();
      }

      Chat? targetChat;
      for (var chat in chatController.chats) {
        if (chat.userInfo?.userId == notification.userId) {
          targetChat = chat;
          break;
        }
      }
      log("targetChat>>>$targetChat");
      // If chat room exists, navigate to it
      if (targetChat != null) {
        Widgets.hideLoader();
        chatController.selectedChat.value = targetChat;
        Get.to(() => ChatView());
      } else {}
    } catch (e) {
      Widgets.hideLoader();
    }
  }

  // void submitReview(BuildContext context, String userId, int rating, String review) {
  //   // Implement API call to submit review
  //   homeController.submitReview(context, userId, rating, review);
  // }
}
