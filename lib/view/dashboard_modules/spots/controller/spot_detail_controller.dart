import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:foilup/controller/user_controller.dart';
import 'package:foilup/core/constants/color_constants.dart';
import 'package:foilup/core/utils/utils.dart';
import 'package:foilup/view/dashboard_modules/spots/model/spot_detail_model.dart';
import 'package:foilup/view/dashboard_modules/spots/model/spot_model.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';

import 'package:url_launcher/url_launcher.dart';

import '../../../../core/constants/api_endpoints.dart';
import '../../../../core/services/http_service.dart';
import '../../../../core/widgets/widgets.dart';
import 'package:http/http.dart' as http;

class SpotDetailController extends GetxController {
  late UserController userController;
  TextEditingController reviewController = TextEditingController();
  TextEditingController reportController = TextEditingController();
  RxBool agreeTerms = false.obs;
  Spot selectSpotCard = Spot();
  double? ratingStars = 0;
  bool? isDetailLoading = false;
  SpotData selectSpotCardDetail = SpotData();
  List<PublicSession>? todaySessions = [];
  List<PublicSession>? upcomingSessions = [];
  RxList<String> selectedImageList = <String>[].obs;

  @override
  void onInit() {
    userController = Get.find();
    super.onInit();
  }
  uploadSpotImages() async {


   if(agreeTerms.value==false)
{return Widgets.showSnackBar("Alert", "Please check agreement first to upload images");}
    Widgets.showLoader("Loading");
    var request = http.MultipartRequest('POST',
        Uri.parse('${Endpoints.baseURL}${Endpoints.requestSpotImage}'));
    for (int i = 0; i < selectedImageList.length; i++) {
      var pic =
      await http.MultipartFile.fromPath('images[]', selectedImageList[i]);

      request.files.add(pic);
    }

    request.headers['Authorization'] = 'Bearer ${userController.token ?? ""}';
   request.fields['spot_id'] = selectSpotCardDetail.spot!.id.toString();

    var response = await request.send();

    Widgets.hideLoader();

    if (response.statusCode == 200) {
      var data = await response.stream.bytesToString();
      var decodedData = jsonDecode(data);

      if (decodedData['status'] == true) {Get.back();
        Widgets.showSnackBar("Success", "Request for images successfully sent");
      } else {
        Widgets.showSnackBar("Error", "Something went wrong");
      }
    } else {
      throw Exception('Failed to send data to the server');
    }
  }

  void resetState() {
    selectedImageList.clear();
    agreeTerms.value = false;
  }
  pickSpotImage(ImageSource source) async {
    try {Get.back();
    var image = await ImagePicker()
        .pickImage(source: source, requestFullMetadata: false);
    if (image!.path.isNotEmpty) {
      final directory = await getTemporaryDirectory();
      String newPath = '${directory.path}/compressed_image_${Utils.generateUniqueNumber()}.jpg';
     print(newPath);
      var result = await FlutterImageCompress.compressAndGetFile(
        image.path,
        newPath,
        quality: 50,

      );
      if (result != null && result.path.isNotEmpty) {
        selectedImageList.add(result.path);
      }

    }
    } catch (err) {

    }
  }

   removeImagesFromList(int index) {
    selectedImageList.removeAt(index);
  }
   selectAgreement(bool value) {
    agreeTerms.value = value;
  }
  fetchSpotDetails() async {
    // try {
      isDetailLoading = true;
      update();
      var response =
          await ApiService.getData("${Endpoints.spot}/${selectSpotCard.id}");
      isDetailLoading = false;

      if (response.status == true) {
        todaySessions?.clear();
        upcomingSessions?.clear();
        selectSpotCardDetail = SpotData.fromJson(response.data);
        if (selectSpotCardDetail.spot!.publicScheduledSessions!.isNotEmpty) {
          selectSpotCardDetail.spot!.publicScheduledSessions
              ?.forEach((element) {
            final today = DateTime.now();
            final formattedToday = DateFormat('dd.MM.yyyy').format(today);


            if (formattedToday == element.date) {
              todaySessions?.add(element);
            } else {
              upcomingSessions?.add(element);
            }
          });
        }
        update();
      } else {}
    // } catch (e) {
    //   print(e.toString());
    // } finally {
    //   isDetailLoading = false;
    //   update();
    // }
  }

   addFavSpot() async {
    try {
      Widgets.showLoader("loading...");
      var response = await ApiService.postData(
          Endpoints.addFavSpot, {"spot_id": selectSpotCard.id});
      Widgets.hideLoader();

      if (response.status == true) {
        fetchSpotDetails();
      } else {
        Widgets.showSnackBar("Alert", response.message ?? "");
      }
    } catch (e) {
      print(e.toString());
    } finally {
      Widgets.hideLoader();
    }
    return null;
  }

  submitRatings() async {
    try {
      Get.back();
      Widgets.showLoader("Submitting Review");
      var data = {
        'stars': ratingStars?.toInt(),
        'spot_id': selectSpotCardDetail.spot?.id.toString(),
        'description': reviewController.text
      };

      var response = await ApiService.postData(Endpoints.rating, data);
      Widgets.hideLoader();
      if (response.status == true) {
        Widgets.showSnackBar("Success", response.message ?? "");
        fetchSpotDetails();
      } else {}
    } catch (e) {
      log(e.toString());
    } finally {
      Widgets.hideLoader();
    }
  }
  submitReport() async {
    try {

      if(reportController.text.isEmpty)
        {
          Widgets.showSnackBar("Alert", "Fill report field");
          return ;
        }
      Get.back();
      Widgets.showLoader("Submitting Report");
      var data = {
        'spot_id': selectSpotCardDetail.spot?.id.toString(),
        'reason': reportController.text
      };

      var response = await ApiService.postData(Endpoints.report, data);
      Widgets.hideLoader();
      if (response.status == true) {
        Widgets.showSnackBar("Success", response.message ?? "");
        fetchSpotDetails();
      } else {}
    } catch (e) {
      log(e.toString());
    } finally {
      Widgets.hideLoader();
    }
  }
  submitVerificationSpotRequest() async {
    try {


      Get.back();
      Widgets.showLoader("Loading..");
      var data = {
        'spot_id': selectSpotCardDetail.spot?.id.toString()
      };

      var response = await ApiService.postData(Endpoints.verifySpot, data);
      Widgets.hideLoader();
      if (response.status == true) {
        Widgets.showSnackBar("Success", response.message ?? "");
        fetchSpotDetails();
      } else {}
    } catch (e) {
      log(e.toString());
    } finally {
      Widgets.hideLoader();
    }
  }

  selectRatingStars(double value) {
    ratingStars = value;
    update();
  }

  showDirections() async {

     String mapUrl = '';
    if (Platform.isIOS) {
       mapUrl =
      'https://maps.apple.com/?daddr=${selectSpotCardDetail.spot?.latitude},${selectSpotCardDetail.spot?.longitude}';
    } else {
       mapUrl =
      'https://www.google.com/maps/dir/?api=1&destination=${selectSpotCardDetail.spot?.latitude},${selectSpotCardDetail.spot?.longitude}&travelmode=driving';
    }
    if (await canLaunchUrl(Uri.parse(mapUrl))) {
      await launchUrl(Uri.parse(mapUrl));
    } else {
      throw 'Unable open the map.';
    }
  }
  launchWebsite(String url) async {

    if (!await launchUrl(Uri.parse(url))) {
      throw Exception('Could not launch $url');
    }
  }

  uploadPicture(String path) async {


    Widgets.showLoader("Loading");
    var request = http.MultipartRequest('POST',
        Uri.parse('${Endpoints.baseURL}${Endpoints.requestSpotImage}'));
    var pic = await http.MultipartFile.fromPath('images[]', path ?? "");
    request.files.add(pic);
    request.headers['Authorization'] = 'Bearer ${userController.token ?? ""}';
request.fields['spot_id'] =selectSpotCardDetail.spot!.id.toString();
    var response = await request.send();

    Widgets.hideLoader();

    if (response.statusCode == 200) {
      var data = await response.stream.bytesToString();
      var decodedData = jsonDecode(data);

      if (decodedData['status'] == true) {

        Widgets.showSnackBar("Success","Spot image request sent" );
      } else {
        Widgets.showSnackBar("Error", "Something went wrong");
      }
    } else {
      throw Exception('Failed to send data to the server');
    }
  }

}
