class TrackGraphData {
  bool? status;
  String? message;
  String? overallHours;
  String? currentMonthHours;
  String? totalStarts;
  GraphData? graphData;

  TrackGraphData(
      {this.status,
        this.message,
        this.overallHours,
        this.currentMonthHours,
        this.totalStarts,
        this.graphData});

  TrackGraphData.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    overallHours = json['overall_hours']!=null?json['overall_hours'].toString():"0";
    currentMonthHours = json['current_month_hours']!=null.toString()? json['current_month_hours'].toString():"0";
    totalStarts = json['total_starts']!=null?json['total_starts'].toString():"0";
    graphData = json['graph_data'] != null
        ? new GraphData.fromJson(json['graph_data'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['overall_hours'] = this.overallHours;
    data['current_month_hours'] = this.currentMonthHours;
    data['total_starts'] = this.totalStarts;
    if (this.graphData != null) {
      data['graph_data'] = this.graphData!.toJson();
    }
    return data;
  }
}

class GraphData {
  List<String>? weekdays;
  List<num>? hours;
  List<num>? starts;
  List<num>? mood;
  BestMood? bestMood;

  GraphData({this.weekdays, this.hours, this.starts, this.mood, this.bestMood});

  GraphData.fromJson(Map<String, dynamic> json) {
    weekdays = json['weekdays'].cast<String>();
    hours = json['hours'].cast<int>();
    starts = json['starts'].cast<int>();
    mood = json['mood'].cast<int>();
    bestMood = json['best_mood'] != null
        ? new BestMood.fromJson(json['best_mood'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['weekdays'] = this.weekdays;
    data['hours'] = this.hours;
    data['starts'] = this.starts;
    data['mood'] = this.mood;
    if (this.bestMood != null) {
      data['best_mood'] = this.bestMood!.toJson();
    }
    return data;
  }
}

class BestMood {
  num? level;
  String? date;

  BestMood({this.level, this.date});

  BestMood.fromJson(Map<String, dynamic> json) {
    level = json['level']??0;
    date = json['date'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['level'] = this.level;
    data['date'] = this.date;
    return data;
  }
}
