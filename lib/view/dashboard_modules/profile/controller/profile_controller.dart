import 'dart:convert';

import 'package:country_picker/country_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:foilup/controller/user_controller.dart';
import 'package:foilup/core/constants/color_constants.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';

import '../../../../core/constants/api_endpoints.dart';
import '../../../../core/services/http_service.dart';
import '../../../../core/utils/utils.dart';
import '../../../../core/widgets/text_widgets.dart';
import '../../../../core/widgets/widgets.dart';
import '../../../../model/user_model.dart';
import 'package:http/http.dart' as http;

import '../../sessions/controller/session_controller.dart';
import '../../spots/model/spot_detail_model.dart';

class ProfileController extends GetxController {
  bool? showPersonalInfo = true;
  bool? showInterestInfo = true;
  bool? showNotificationInfo = true;
  bool? notifyGeneralNews = false;
  bool? notifyRequests = false;
  bool? notifyFriendSession = false;
  bool? notifyFavSpots = false;
  bool? agreeTerms = false;
  bool? showSkillsInfo = true;
  bool? isEditMode = false;
  List<String> foilBrandList = [];
  String? imagePath;
  List<String> otherInterestsValues = List.generate(5, (index) => '');
  selectAgreement(bool value) {
    agreeTerms = value;
    update();
  }

  List<String> selectedImageList = [];
  List<Images> selectedImageUrlsList = [];
  String? personalInfoVisibilty = "public";
  String? interestsInfoVisibilty = "public";
  String? skillsInfoVisibilty = "public";
  String? gender;
  String? birthDate;
  String? countryName;
  String? wingfoilingValue;
  String? kitefoilingValue;
  String? proneSurffoilingValue;
  String? wakefoilingValue;
  String? eFoilingValue;
  String? pumpFoilLevel;
  String? foilBrand;
  late UserController userController;
  TextEditingController frontwingController = TextEditingController();
  TextEditingController backwingController = TextEditingController();
  TextEditingController fuselageController = TextEditingController();
  TextEditingController boardBrandController = TextEditingController();
  TextEditingController boardModelController = TextEditingController();
  late SessionController sessionController;

  TextEditingController foilBrandController = TextEditingController();
  TextEditingController mastController = TextEditingController();
  TextEditingController birthDateController = TextEditingController();
  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController countryController = TextEditingController();
  TextEditingController usernameController = TextEditingController();
  TextEditingController familyNameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  TextEditingController aboutMeController = TextEditingController();

  @override
  void onInit() {
    userController = Get.find();
    sessionController = Get.find();
    fetchDynamicFields();
    fetchUserDetails();
    super.onInit();
  }

  fetchUserDetails() async {
    try {
      var response = await ApiService.getData(Endpoints.fetchProfile);

      if (response.status == true) {
        UserModel userModel = UserModel.fromJson(response.data['user']);

        await userController.saveUser(userModel, userController.token ?? "",
            userController.password ?? "");
        await userController.fetchUser();
        updateUserInformationToUI();
        update();
      } else {}
    } catch (e) {
    } finally {}
  }

  deleteImage(String id) async {
    try {
      Widgets.showLoader("Deleting picture");
      var response = await ApiService.postData(
          Endpoints.deleteFoilerImage, {"foiling_picture_id": id});
      Widgets.hideLoader();
      if (response.status == true) {
        UserModel userModel = UserModel.fromJson(response.data['user']);

        await userController.saveUser(userModel, userController.token ?? "",
            userController.password ?? "");
        await userController.fetchUser();
        updateUserInformationToUI();
        update();
      } else {}
    } catch (e) {
    } finally {
      Widgets.hideLoader();
    }
  }

  deleteProfileImage() async {
    try {
      Widgets.showLoader("Deleting Profile picture");
      var response = await ApiService.getData(Endpoints.deleteProfileImage);
      Widgets.hideLoader();
      if (response.status == true) {Widgets.showSnackBar("Deleted", "Profile picture deleted");
        UserModel userModel = UserModel.fromJson(response.data['user']);

        await userController.saveUser(userModel, userController.token ?? "",
            userController.password ?? "");
        await userController.fetchUser();
        updateUserInformationToUI();
        update();
      } else {}
    } catch (e) {
    } finally {
      Widgets.hideLoader();
    }
  }

  updateUserProfile() async {
    try {
      if (firstNameController.text.isEmpty) {
        return Widgets.showSnackBar("Alert", "First name is empty");
      }
      if (lastNameController.text.isEmpty) {
        return Widgets.showSnackBar("Alert", "Last name is empty");
      }
      if (countryName == null) {
        return Widgets.showSnackBar("Alert", "Country name is empty");
      }

      Widgets.showLoader("Saving Profile...");
      var data = {
        'email': emailController.text,
        'first_name': firstNameController.text,
        'last_name': lastNameController.text,
        'username': usernameController.text,
        'country': countryName,
        'family_name': familyNameController.text,
        'gender': gender,
        'about_me': aboutMeController.text,
        'date_of_birth': birthDateController.text,
        'profile_information_visiblity': personalInfoVisibilty ?? "public",
        'pumpfoil_level': pumpFoilLevel,
        'foil_brand': foilBrand ?? "",
        'frontwing_model': frontwingController.text,
        'backwing_model': backwingController.text,
        'fuselage_model': fuselageController.text,
        'mast_length': mastController.text,
        'board_brand': boardBrandController.text,
        'board_model': boardModelController.text,
        'equipments_information_visiblity': skillsInfoVisibilty ?? "public",
        'wingfoiling': otherInterestsValues[0] ?? "",
        'kitefoiling': otherInterestsValues[1] ?? "",
        'prone_surffoiling': otherInterestsValues[2] ?? "",
        'wakefoiling': otherInterestsValues[3] ?? "",
        'e_foiling': otherInterestsValues[4] ?? "",
        'interest_information_visiblity': interestsInfoVisibilty ?? "public",
      };
      var response = await ApiService.postData(Endpoints.updateProfile, data);
      Widgets.hideLoader();

      if (response.status == true) {
        isEditMode = false;
        Widgets.showSnackBar("Success", response.message ?? "");
        UserModel userModel = UserModel.fromJson(response.data['user']);
        await userController.saveUser(userModel, userController.token ?? "",
            userController.password ?? "");
        await userController.fetchUser();
        update();
        updateUserInformationToUI();
      } else {
        Widgets.showSnackBar("Error", response.message ?? "");
      }
    } catch (e) {
      Widgets.showSnackBar("Error", e.toString());
    } finally {
      Widgets.hideLoader();


      sessionController.fetchTrackGraphDataByMonth( DateFormat('MM-yyyy').format(sessionController.selectedDate));
      sessionController.fetchLongestRun();
      sessionController.fetchNumberSession();
    }
  }

  updateNotificationSetting(String category, int value) async {
    try {
      var data = {"value": value, "category": category};
      var response =
          await ApiService.postData(Endpoints.updateNotificationSetting, data);
      Widgets.hideLoader();

      if (response.status == true) {
      } else {
        Widgets.showSnackBar("Error", response.message ?? "");
      }
    } catch (e) {
      Widgets.showSnackBar("Error", e.toString());
    } finally {
      // fetchUserDetails();
    }
  }

  changeProfilePic() async {
    Widgets.showLoader("Loading");
    var request = http.MultipartRequest('POST',
        Uri.parse('${Endpoints.baseURL}${Endpoints.updateProfileImage}'));
    var pic = await http.MultipartFile.fromPath('image', imagePath ?? "");
    request.files.add(pic);
    request.headers['Authorization'] = 'Bearer ${userController.token ?? ""}';

    var response = await request.send();

    Widgets.hideLoader();

    if (response.statusCode == 200) {
      var data = await response.stream.bytesToString();
      var decodedData = jsonDecode(data);

      if (decodedData['status'] == true) {imagePath=null;
        fetchUserDetails();

        Widgets.showSnackBar("Success", "Image Updated Successfully");
      } else {
        Widgets.showSnackBar("Error", "Something went wrong");
      }
    } else {
      throw Exception('Failed to send data to the server');
    }
  }

  changeFoilingPic(String path) async {
    Widgets.showLoader("Loading");
    var request = http.MultipartRequest('POST',
        Uri.parse('${Endpoints.baseURL}${Endpoints.updateFoilingImage}'));
    var pic = await http.MultipartFile.fromPath('images[]', path ?? "");
    request.files.add(pic);
    request.headers['Authorization'] = 'Bearer ${userController.token ?? ""}';

    var response = await request.send();

    Widgets.hideLoader();

    if (response.statusCode == 200) {
      var data = await response.stream.bytesToString();
      var decodedData = jsonDecode(data);

      if (decodedData['status'] == true) {
        UserModel userModel = UserModel.fromJson(decodedData['user']);
        userController.saveUser(userModel, userController.token ?? "",
            userController.password ?? "");
        userController.fetchUser();
        update();
        Widgets.showSnackBar("Success", "Image Updated Successfully");
      } else {
        Widgets.showSnackBar("Error", "Something went wrong");
      }
    } else {
      throw Exception('Failed to send data to the server');
    }
  }

  updateUserInformationToUI() {
    userController.fetchUser();
    firstNameController.text = userController.userModel?.firstName ?? "";
    aboutMeController.text = userController.userModel?.aboutMe ?? "";
    lastNameController.text = userController.userModel?.lastName ?? "";
    usernameController.text = userController.userModel?.username ?? "";
    familyNameController.text = userController.userModel?.familyName ?? "";
    emailController.text = userController.userModel?.email ?? "";
    gender = userController.userModel?.gender;
    birthDateController.text = userController.userModel?.dateOfBirth ?? "";
    countryName = userController.userModel?.country ?? "";
    personalInfoVisibilty =
        userController.userModel?.profileInformationVisiblity ?? "";
    skillsInfoVisibilty = userController
            .userModel?.equipmentsAndSkills?.equipmentsInformationVisiblity ??
        "";
    pumpFoilLevel =
        userController.userModel?.equipmentsAndSkills?.pumpfoilLevel ?? "";
    foilBrand = userController.userModel?.equipmentsAndSkills?.foilBrand ?? "";
    frontwingController.text =
        userController.userModel?.equipmentsAndSkills?.frontwingModel ?? "";
    backwingController.text =
        userController.userModel?.equipmentsAndSkills?.backwingModel ?? "";
    fuselageController.text =
        userController.userModel?.equipmentsAndSkills?.fuselageModel ?? "";
    mastController.text =
        userController.userModel?.equipmentsAndSkills?.mastLength ?? "";
    boardBrandController.text =
        userController.userModel?.equipmentsAndSkills?.boardBrand ?? "";
    boardModelController.text =
        userController.userModel?.equipmentsAndSkills?.boardModel ?? "";
    interestsInfoVisibilty =
        userController.userModel?.interests?.interestInformationVisiblity ?? "";

    toggleSelectionForInterests(
        0, userController.userModel?.interests?.wingfoiling ?? "");
    toggleSelectionForInterests(
        1, userController.userModel?.interests?.kitefoiling ?? "");
    toggleSelectionForInterests(
        2, userController.userModel?.interests?.proneSurffoiling ?? "");
    toggleSelectionForInterests(
        3, userController.userModel?.interests?.wakefoiling ?? "");
    toggleSelectionForInterests(
        4, userController.userModel?.interests?.eFoiling ?? "");

    notifyGeneralNews =
        userController.userModel?.notifyGeneralNews == 1 ? true : false;
    notifyFriendSession =
        userController.userModel?.notifyFriendSession == 1 ? true : false;
    notifyFavSpots =
        userController.userModel?.notifyFavSession == 1 ? true : false;
    notifyRequests =
        userController.userModel?.notifyFriendRequest == 1 ? true : false;
    update();
  }

  showPersonalInfoVisible() {
    showPersonalInfo = showPersonalInfo == true ? false : true;
    update();
  }

  showInterestInfoVisible() {
    showInterestInfo = showInterestInfo == true ? false : true;
    update();
  }

  showNotificationsInfoVisible() {
    showNotificationInfo = showNotificationInfo == true ? false : true;
    update();
  }

  changeNotificationSetting(bool value, String category) {
    if (category == "notify_friend_requests") {
      notifyRequests = value;
      update();
      updateNotificationSetting(
          "notify_friend_requests", value == true ? 1 : 0);
    } else if (category == "notify_friends_sessions") {
      notifyFriendSession = value;
      update();
      updateNotificationSetting(
          "notify_friends_sessions", value == true ? 1 : 0);
    } else if (category == "notify_favourite_spot_sessions") {
      notifyFavSpots = value;
      update();
      updateNotificationSetting(
          "notify_favourite_spot_sessions", value == true ? 1 : 0);
    } else {
      notifyGeneralNews = value;
      update();
      updateNotificationSetting("notify_general_news", value == true ? 1 : 0);
    }
  }

  changeProfileMode(bool value) {
    isEditMode = value;
    update();
  }

  showSkillsInfoVisible() {
    showSkillsInfo = showSkillsInfo == true ? false : true;
    update();
  }

  selectGender(int index) {
    if (index == 0) {
      gender = 'Male';
    } else if (index == 1) {
      gender = 'Female';
    } else {
      gender = 'Others';
    }
    update();
  }

  toggleSelectionForInterests(int index, String value) {
    otherInterestsValues[index] = value;
    update();
  }

  changePersonalInfoVisibility(int index) {
    if (index == 0) {
      personalInfoVisibilty = 'public';
    } else if (index == 1) {
      personalInfoVisibilty = 'friends';
    } else {
      personalInfoVisibilty = 'private';
    }
    update();
  }

  changeInterestInfoVisibility(int index) {
    if (index == 0) {
      interestsInfoVisibilty = 'public';
    } else if (index == 1) {
      interestsInfoVisibilty = 'friends';
    } else {
      interestsInfoVisibilty = 'private';
    }
    update();
  }

  changeSkillInfoVisibility(int index) {
    if (index == 0) {
      skillsInfoVisibilty = 'public';
    } else if (index == 1) {
      skillsInfoVisibilty = 'friends';
    } else {
      skillsInfoVisibilty = 'private';
    }
    update();
  }

  selectBirthDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: ColorConstants.primaryColor,
                ),
          ),
          child: child!,
        );
      },
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      birthDate = DateFormat('yyyy-MM-dd').format(picked);
      birthDateController.text = birthDate ?? "";
      update();
    }
  }

  selectCountry(BuildContext context) {
    showCountryPicker(
      context: context,
      countryListTheme: CountryListThemeData(
        flagSize: 25,
        backgroundColor: Colors.white,
        textStyle: const TextStyle(fontSize: 16, color: Colors.blueGrey),
        bottomSheetHeight: 500, // Optional. Country list modal height
        //Optional. Sets the border radius for the bottomsheet.
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        ),
        //Optional. Styles the search field.
        inputDecoration: InputDecoration(
          labelText: 'Search',
          hintText: 'Start typing to search',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderSide: BorderSide(
              color: const Color(0xFF8C98A8).withOpacity(0.2),
            ),
          ),
        ),
      ),
      onSelect: (Country country) {
        countryName = country.name;
        update();
      },
    );
  }

  selectPumpFoilValue(String value) {
    pumpFoilLevel = value;
    update();
    Get.back();
  }

  selectFoilBrandValue(String value) {
    foilBrand = value;
    update();
    Get.back();
  }

  selectPumpFoilLevel(BuildContext context) {
    showModalBottomSheet(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      context: context,
      builder: (BuildContext bc) {
        return Padding(
          padding: const EdgeInsets.all(15.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Center(
                child: Container(
                  width: 60,
                  height: 2,
                  color: Colors.grey,
                ),
              ),
              Widgets.heightSpaceH3,
              Widgets.textTapped(
                  title: "Beginner",
                  subTitle:
                      "I can land on the board but do not yet master more than 3 pumps",
                  onTap: () {
                    selectPumpFoilValue("Beginner");
                    // Close the bottom sheet
                  }),
              const Divider(),
              Widgets.textTapped(
                  title: "Intermediate",
                  subTitle:
                      "I can manage the dockstart and do more than 10 pumps",
                  onTap: () {
                    selectPumpFoilValue("Intermediate");
                    // Close the bottom sheet
                  }),
              const Divider(),
              Widgets.textTapped(
                  title: "Advanced intermediate",
                  subTitle:
                      " can manage the dockstart, pump for more than 20 seconds and manage front - and back side turns",
                  onTap: () {
                    selectPumpFoilValue("Advanced intermediate");
                    // Close the bottom sheet
                  }),
              const Divider(),
              Widgets.textTapped(
                  title: "Advanced",
                  subTitle: " I can pump stable for more than 45 seconds",
                  onTap: () {
                    selectPumpFoilValue("Advanced");
                    // Close the bottom sheet
                  }),
              const Divider(),
              Widgets.textTapped(
                  title: "Wakethief",
                  subTitle: " I can pump for more than 60 seconds",
                  onTap: () {
                    selectPumpFoilValue("Wakethief");
                    // Close the bottom sheet
                  }),
              const Divider(),
              Widgets.textTapped(
                  title: "Power Pumper",
                  subTitle: " I can pump for more than 2 minutes",
                  onTap: () {
                    selectPumpFoilValue("Power Pumper");
                    // Close the bottom sheet
                  }),
            ],
          ),
        );
      },
    );
  }

  dropdownFoilType(BuildContext context) {
    showModalBottomSheet(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      context: context,
      builder: (BuildContext bc) {
        return Padding(
          padding: const EdgeInsets.all(15.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Center(
                child: Container(
                  width: 60,
                  height: 2,
                  color: Colors.grey,
                ),
              ),
              Widgets.heightSpaceH3,
              Center(
                  child: Texts.textBlock("Select Foil Brand",
                      size: 22, color: ColorConstants.primaryColor)),
              Widgets
                  .heightSpaceH3, // Replace Widgets.heightSpaceH3 with SizedBox
              Expanded(
                child: ListView.separated(
                  physics: const BouncingScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: foilBrandList.length,
                  itemBuilder: (BuildContext context, int index) {
                    return ListTile(
                      dense: true,
                      contentPadding: EdgeInsets.zero,
                      horizontalTitleGap: 2,
                      onTap: () {
                        selectFoilBrandValue(foilBrandList[index]);
                      },
                      leading: Icon(Icons.check_box_outline_blank,
                          color: ColorConstants.secondaryColor),
                      title: Texts.textMedium(foilBrandList[index],
                          color: ColorConstants.primaryColor, size: 15),
                    );
                  },
                  separatorBuilder: (BuildContext context, int index) {
                    return const Divider();
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  selectionDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Profile Picture',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: const Text('select any option'),
          actionsPadding: const EdgeInsets.all(20),
          actions: <Widget>[
            InkWell(
              onTap: () {
                Get.back();
                pickImage(ImageSource.gallery);
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
                decoration: BoxDecoration(
                  color: ColorConstants.primaryColor,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12.withOpacity(0.1),
                      spreadRadius: 2,
                      blurRadius: 5,
                      offset: const Offset(0, 3), // changes position of shadow
                    ),
                  ],
                  borderRadius: const BorderRadius.all(Radius.circular(5)),
                ),
                child: const Text(
                  "Gallery",
                  style:
                      TextStyle(fontFamily: "MetroBold", color: Colors.white),
                ),
              ),
            ),
            InkWell(
              onTap: () {
                Get.back();
                pickImage(ImageSource.camera);
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
                decoration: BoxDecoration(
                  border: Border.all(color: ColorConstants.primaryColor),
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12.withOpacity(0.1),
                      spreadRadius: 2,
                      blurRadius: 5,
                      offset: const Offset(0, 3), // changes position of shadow
                    ),
                  ],
                  borderRadius: const BorderRadius.all(Radius.circular(5)),
                ),
                child: const Text(
                  "Camera",
                  style:
                      TextStyle(fontFamily: "MetroBold", color: Colors.black),
                ),
              ),
            ),
            const SizedBox(
              width: 5,
            )
          ],
        );
      },
    );
  }

  fetchDynamicFields() async {
    try {
      var response = await ApiService.getData(Endpoints.dynamicFields);

      if (response.status == true) {
        foilBrandList?.clear();
        foilBrandList =
            (response.data['Foil Brand'] as List).whereType<String>().toList();

        update();
      }
    } catch (e) {
      print(e);
    } finally {}
  }

  pickImage(ImageSource source) async {
    try {
      var image = await ImagePicker()
          .pickImage(source: source, requestFullMetadata: false);
      if (image!.path.isNotEmpty) {
        final directory = await getTemporaryDirectory();
        String newPath =
            '${directory.path}/compressed_image_${Utils.generateUniqueNumber()}.jpg';

        var result = await FlutterImageCompress.compressAndGetFile(
          image.path,
          newPath,
          quality: 50, // Set the compression quality (0 to 100)
        );

        imagePath = result?.path;
        update();
        changeProfilePic();
      }
    } catch (err) {
      print(err);
    }
  }

  pickFoilingImage(ImageSource source) async {
    try {
      Get.back();
      var image = await ImagePicker()
          .pickImage(source: source, requestFullMetadata: false);
      if (image!.path.isNotEmpty) {
        final directory = await getTemporaryDirectory();
        String newPath =
            '${directory.path}/compressed_image_${Utils.generateUniqueNumber()}.jpg';

        var result = await FlutterImageCompress.compressAndGetFile(
          image.path,
          newPath,
          quality: 50, // Set the compression quality (0 to 100)
        );
        changeFoilingPic(result?.path ?? "");
      }
    } catch (err) {
      print(err);
    }
  }
}
